import { ChangeDetectionStrategy, ChangeDetectorRef, Component, <PERSON>E<PERSON>ter, On<PERSON><PERSON>roy, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { takeUntil } from 'rxjs';
import { AppState } from 'src/app/app.reducer';
import { FetchAllSources } from 'src/app/reducers/global-settings/global-settings.actions';
import { getAllSources, getAllSourcesLoading } from 'src/app/reducers/global-settings/global-settings.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { NotificationsService } from 'angular2-notifications';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';
import { Title } from '@angular/platform-browser';
import { SourceService } from 'src/app/services/controllers/source.service';
import { INTEGRATION_LIST } from 'src/app/app.constants';
import { LeadSource, IntegrationSource } from 'src/app/app.enum';
import { environment } from 'src/environments/environment';
import { ManageSourceIndexDBService } from 'src/app/services/shared/managesource.indexdb.service';

@Component({
  selector: 'manage-source',
  templateUrl: './manage-source.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ManageSourceComponent implements OnInit, OnDestroy {
  @ViewChild('sourceWarningPopup') sourceWarningPopup: TemplateRef<any>;
  readonly NON_DISABLEABLE_SOURCES: number[] = [
    0,  // Direct source
    23, // QR Code source
  ];

  private stopper: EventEmitter<void> = new EventEmitter<void>();
  s3BucketUrl = environment.s3ImageBucketURL;
  sources: any[] = [];
  filteredSources: any[] = [];
  isSourcesLoading: boolean = false;
  searchTerm: string = '';
  permissions: Set<unknown>;
  canHide: boolean = false;
  canUnhide: boolean = false;
  sourceCountData: any = null;
  currentSource: any = null;
  isToggleInProgress: boolean = false;

  constructor(
    private store: Store<AppState>,
    private router: Router,
    private headerTitle: HeaderTitleService,
    private notificationService: NotificationsService,
    private modalService: BsModalService,
    public metaTitle: Title,
    private sourceService: SourceService,
    private cdr: ChangeDetectorRef,
    public modalRef: BsModalRef,
    private manageSourceIndexDBService: ManageSourceIndexDBService
  ) { }

  ngOnInit(): void {
    this.headerTitle.setLangTitle('Global Config');
    this.metaTitle.setTitle('CRM | Global Config');

    this.store.dispatch(new FetchAllSources());
    this.store
      .select(getAllSources)
      .pipe(takeUntil(this.stopper))
      .subscribe((sources: any[]) => {
        this.sources = sources.map(source => ({
          ...source,
          isEnabled: this.NON_DISABLEABLE_SOURCES.includes(source.value) ? true : source.isEnabled
        }))
        // this.sources = this.sources?.filter((source: any) =>
        //   INTEGRATION_LIST?.some((integration: any) => integration.value === source.value));
        this.updateFilteredSources();
        this.cdr.markForCheck();
      });

    this.store
      .select(getAllSourcesLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((loading: boolean) => {
        this.isSourcesLoading = loading;
        this.cdr.markForCheck();
      });

    this.store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        this.permissions = new Set(permissions);
        this.canHide = permissions?.includes('Permissions.Sources.Hide');
        this.canUnhide = permissions?.includes('Permissions.Sources.UnHide');
        this.cdr.markForCheck();
      });
  }

  updateFilteredSources(): void {
    let result = [...this.sources];
    if (this.searchTerm && this.searchTerm.trim() !== '') {
      const searchTermLower = this.searchTerm.toLowerCase().trim();
      result = result.filter(source => {
        const displayName = source.displayName || '';
        const name = source.name || '';
        return displayName.toLowerCase().includes(searchTermLower) ||
          name.toLowerCase().includes(searchTermLower);
      });
    }

    result = result.sort((a, b) => {
      if (a.isEnabled && !b.isEnabled) return -1;
      if (!a.isEnabled && b.isEnabled) return 1;
      return a.displayName.localeCompare(b.displayName);
    });
    this.filteredSources = result;
    this.cdr.markForCheck();
  }

  filterSources(): any[] {
    return this.filteredSources;
  }

  toggleSourceVisibility(source: any): void {
    console.log('toggleSourceVisibility',source)
    if (this.NON_DISABLEABLE_SOURCES.includes(source.value)) {
      const sourceName = source.value === 0 ? 'Direct' : (source.value === 23 ? 'QR Code' : source.displayName);
      this.notificationService.info(`${sourceName} source cannot be disabled.`);
      return;
    }

    if (this.isToggleInProgress) {
      return;
    }
    if ((!this.canHide && source.isEnabled) || (!this.canUnhide && !source.isEnabled)) {
      return;
    }

    // Set current source immediately to ensure correct source name in popup
    this.currentSource = source;

    if (!source.isEnabled) {
      this.showStandardConfirmation(source);
      return;
    }
    this.isToggleInProgress = true;
    this.cdr.markForCheck();

    this.sourceService.getLeadAndDataCount(source.value)
      .subscribe({
        next: (response: any) => {
          this.isToggleInProgress = false;
          this.cdr.markForCheck();
          if (response.succeeded && response.data) {
            this.sourceCountData = response.data;
            if (this.sourceCountData.leadCount > 0 || this.sourceCountData.prospectCount > 0) {
              this.modalRef = this.modalService.show(
                this.sourceWarningPopup,
                Object.assign(
                  {},
                  {
                    class: 'modal-500 top-modal ph-modal-unset',
                    ignoreBackdropClick: true,
                  }
                )
              );
            } else {
              this.showStandardConfirmation(source);
            }
          } else {
            this.showStandardConfirmation(source);
          }
        },
        error: (error) => {
          this.isToggleInProgress = false;
          this.cdr.markForCheck();
          console.error('Error checking source counts:', error);
          this.notificationService.error('Failed to check source usage. Please try again.');
        }
      });
  }

  showStandardConfirmation(source: any): void {
    console.log('showStandardConfirmation',source)
    const actionText = `${source.isEnabled ? 'disable' : 'enable'}`;
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: actionText,
      title: source.displayName,
      fieldType: 'source',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
          ignoreBackdropClick: false,
        }
      )
    );

    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        this.isToggleInProgress = false;
        this.cdr.markForCheck();
        if (reason == 'confirmed') {
          this.isToggleInProgress = true;
          this.cdr.markForCheck();
          const isEnabled = !source.isEnabled;
          this.sourceService.updateSourceStatus(source.id, isEnabled)
            .subscribe({
              next: () => {
                this.isToggleInProgress = false;
                const message = isEnabled ? 'Source enabled successfully.' : 'Source disabled successfully.';
                this.notificationService.success(message);
                this.manageSourceIndexDBService.clearCaches();
                this.store.dispatch(new FetchAllSources());
                this.cdr.markForCheck();
              },
              error: (error) => {
                this.isToggleInProgress = false;
                console.error('Error updating source status:', error);
                this.notificationService.error('Failed to update source status. Please try again.');
                this.cdr.markForCheck();
              }
            });
        }
      });
    }
  }

  confirmSourceDisable(): void {
    if (this.sourceCountData?.leadCount > 0 || this.sourceCountData?.prospectCount > 0) {
      let message = '';
      if (this.sourceCountData.leadCount > 0 && this.sourceCountData.prospectCount > 0) {
        message = 'Cannot disable source with associated leads and data. Please assign them to another source or convert to direct first.';
      } else if (this.sourceCountData.leadCount > 0) {
        message = 'Cannot disable source with associated leads. Please assign leads to another source or click Convert to Direct.';
      } else if (this.sourceCountData.prospectCount > 0) {
        message = 'Cannot disable source with associated data. Please assign data to another source first or convert to direct.';
      }
      this.notificationService.warn(message);
      return;
    }
    this.modalRef.hide();
    this.isToggleInProgress = true;
    this.cdr.markForCheck();

    // Handle single source operation
    if (this.currentSource) {
      const isEnabled = !this.currentSource.isEnabled;
      this.sourceService.updateSourceStatus(this.currentSource.id, isEnabled)
        .subscribe({
          next: () => {
            this.isToggleInProgress = false;
            const message = isEnabled ? 'Source enabled successfully.' : 'Source disabled successfully.';
            this.notificationService.success(message);
            this.manageSourceIndexDBService.clearCaches();
            this.store.dispatch(new FetchAllSources());
            this.cdr.markForCheck();
          },
          error: (error) => {
            this.isToggleInProgress = false;
            console.error('Error updating source status:', error);
            this.notificationService.error('Failed to update source status. Please try again.');
            this.cdr.markForCheck();
          }
        });
    }
  }

  saveSourceVisibility(): void {
    if (this.isToggleInProgress) {
      return;
    }
  console.log('filteredSources',this.filteredSources)
    let selectedSources = this.filteredSources.filter(source => source.selected);
    const nonDisableableSelected = selectedSources.filter(source =>
      this.NON_DISABLEABLE_SOURCES.includes(source.value)
    );
    console.log('nonDisableableSelected',nonDisableableSelected)
    if (nonDisableableSelected.length > 0) {
      const excludedSourceNames = nonDisableableSelected.map(source =>
        source.value === 0 ? 'Direct' : (source.value === 23 ? 'QR Code' : source.displayName)
      );
      selectedSources = selectedSources.filter(source =>
        !this.NON_DISABLEABLE_SOURCES.includes(source.value)
      );
      this.notificationService.info(`${excludedSourceNames.join(', ')} source(s) cannot be disabled and have been excluded from the operation.`);
    }
    if (selectedSources.length === 0) {
      this.notificationService.warn('Please select at least one source that can be disabled for bulk operation.');
      return;
    }
    const isEnabling = selectedSources.every(source => !source.isEnabled);
    if (isEnabling) {
      this.showBulkConfirmation(selectedSources, true);
      return;
    }
    const disablingSources = selectedSources.filter(source => source.isEnabled);
    if (disablingSources.length === 0) {
      this.showBulkConfirmation(selectedSources, true);
      return;
    }
    this.isToggleInProgress = true;
    this.cdr.markForCheck();
    this.pendingSourceIds = selectedSources.map(source => source.id);
    this.pendingIsEnabled = isEnabling;
    this.isBulkOperation = true;

    const firstDisabledSource = disablingSources[0];
    this.sourceService.getLeadAndDataCount(firstDisabledSource.value)
      .subscribe({
        next: (response: any) => {
          this.isToggleInProgress = false;
          this.cdr.markForCheck();
          if (response.succeeded && response.data) {
            this.sourceCountData = response.data;

            if (this.sourceCountData.leadCount > 0 || this.sourceCountData.prospectCount > 0) {
              this.modalRef = this.modalService.show(
                this.sourceWarningPopup,
                Object.assign(
                  {},
                  {
                    class: 'modal-500 top-modal ph-modal-unset',
                    ignoreBackdropClick: true,
                  }
                )
              );
            } else {
              this.showBulkConfirmation(selectedSources, isEnabling);
            }
          } else {
            this.showBulkConfirmation(selectedSources, isEnabling);
          }
        },
        error: (error) => {
          this.isToggleInProgress = false;
          this.cdr.markForCheck();
          console.error('Error checking source counts:', error);
          this.notificationService.error('Failed to check source usage. Please try again.');
        }
      });
  }

  showBulkConfirmation(selectedSources: any[], isEnabling: boolean): void {
    console.log('showBulkConfirmation',selectedSources)
    const actionText = `${isEnabling ? 'enable' : 'disable'}`;
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: actionText,
      title: 'selected sources',
      fieldType: '',
    };

    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
          ignoreBackdropClick: false,
        }
      )
    );

    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        this.isToggleInProgress = false;
        this.cdr.markForCheck();
        if (reason == 'confirmed') {
          this.isToggleInProgress = true;
          this.cdr.markForCheck();
          const sourceIds = selectedSources.map(source => source.id);
          console.log('sourceIds',sourceIds)
          this.sourceService.bulkUpdateSourceStatus(sourceIds, isEnabling)
            .subscribe({
              next: () => {
                this.isToggleInProgress = false;
                const message = isEnabling ? 'Source(s) enabled successfully.' : 'Source(s) disabled successfully.';
                this.notificationService.success(message);
                this.selectAll = false;
                this.sources = this.sources.map(source => ({
                  ...source,
                  selected: false
                }));
                this.manageSourceIndexDBService.clearCaches();
                this.store.dispatch(new FetchAllSources());
                this.cdr.markForCheck();
              },
              error: (error) => {
                this.isToggleInProgress = false;
                console.error('Error updating source status:', error);
                this.notificationService.error('Failed to update source status. Please try again.');
                this.cdr.markForCheck();
              }
            });
        }
      });
    }
  }

  navigateToLead() {
    this.isToggleInProgress = false;
    this.cdr.markForCheck();
    this.router.navigate(['leads/manage-leads'], {
      queryParams: {
        Source: JSON.stringify(LeadSource[this.currentSource.value]),
        isNavigatedFromSource: true,
      },
    });
    this.modalRef.hide();
  }

  convertToDirect() {
    this.modalRef.hide();
    this.isToggleInProgress = true;
    this.cdr.markForCheck();
    if (this.currentSource) {
      const sourceValue = typeof this.currentSource.value === 'string'
        ? parseInt(this.currentSource.value, 10)
        : this.currentSource.value;
      this.sourceService.convertToDirect(sourceValue)
        .subscribe({
          next: (response) => {
            this.isToggleInProgress = false;
            if (response && response.succeeded) {
              this.notificationService.success('Source converted successfully. Processing in background, please wait..');
              this.manageSourceIndexDBService.clearCaches();
              this.store.dispatch(new FetchAllSources());
              this.cdr.markForCheck();
            } else {
              this.notificationService.error('Failed to convert source to direct. Please try again.');
              this.cdr.markForCheck();
            }
          },
          error: (error) => {
            this.isToggleInProgress = false;
            console.error('Error converting source to direct:', error);
            this.notificationService.error('Failed to convert source to direct. Please try again.');
            this.cdr.markForCheck();
          }
        });
    }
  }

  navigateToData() {
    this.isToggleInProgress = false;
    this.cdr.markForCheck();
    this.router.navigate(['data/manage-data'], {
      queryParams: {
        SourceValue: this.currentSource.value,
        isNavigatedFromSource: true,
      },
    });
    this.modalRef.hide();
  }

  clearSearch(): void {
    this.searchTerm = '';
    this.updateFilteredSources();
    this.cdr.markForCheck();
  }

  goBack(): void {
    this.router.navigate(['/global-config']);
  }

  ngOnDestroy(): void {
    this.stopper.next();
    this.stopper.complete();
  }
}
